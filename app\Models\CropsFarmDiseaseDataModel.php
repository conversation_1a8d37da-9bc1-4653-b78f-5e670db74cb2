<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsFarmDiseaseDataModel extends Model
{
    protected $table = 'crops_farm_disease_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'block_id',
        'crop_id',
        'disease_type_id',
        'disease_name',
        'description',
        'action_reason',
        'number_of_plants',
        'breed',
        'action_date',
        'hectares',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $dateFormat = 'datetime';

    protected $skipValidation = true;
    protected $cleanValidationRules = true;
}
