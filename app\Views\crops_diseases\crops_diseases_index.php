<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">Disease Data Management</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Disease Data</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bug me-2"></i>Farm Blocks - Disease Data Management
                    </h5>
                    <p class="card-text mb-0 text-muted">Select a farm block to view and manage disease data</p>
                </div>
                <div class="card-body">
                    <!-- Success/Error Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="diseaseDataTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Block Code</th>
                                    <th>Farmer Name</th>
                                    <th>Crop Type</th>
                                    <th>Location</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($farm_blocks as $block): ?>
                                <tr>
                                    <td><strong><?= esc($block['block_code']) ?></strong></td>
                                    <td>
                                        <strong><?= esc($block['given_name']) . ' ' . esc($block['surname']) ?></strong><br>
                                        <small class="text-muted">Farmer Code: <?= esc($block['farmer_code'] ?? 'N/A') ?></small>
                                    </td>
                                    <td><span class="badge bg-success"><?= esc($block['crop_name']) ?></span></td>
                                    <td>
                                        <div>
                                            <strong><?= esc($block['village']) ?></strong><br>
                                            <small class="text-muted">
                                                <?= esc($block['ward_name']) ?>, <?= esc($block['llg_name']) ?>, <?= esc($block['district_name']) ?>
                                            </small>
                                        </div>
                                    </td>
                                    <td><span class="badge bg-info">Active</span></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('staff/crops/diseases/view/' . $block['id']) ?>"
                                               class="btn btn-sm btn-info" title="View Disease Data">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#diseaseDataTable').DataTable({
        responsive: true,
        order: [[0, 'asc']],
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        language: {
            search: "Search farm blocks:",
            lengthMenu: "Show _MENU_ blocks per page",
            info: "Showing _START_ to _END_ of _TOTAL_ blocks",
            infoEmpty: "No blocks found",
            infoFiltered: "(filtered from _MAX_ total blocks)"
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
