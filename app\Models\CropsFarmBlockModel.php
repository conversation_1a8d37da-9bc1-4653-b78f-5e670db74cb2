<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsFarmBlockModel extends Model
{
    protected $table = 'crops_farm_blocks';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'exercise_id',
        'farmer_id',
        'crop_id',
        'block_code',
        'org_id',
        'country_id',
        'province_id',
        'district_id',
        'llg_id',
        'ward_id',
        'village',
        'block_site',
        'lon',
        'lat',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $dateFormat = 'datetime';

    protected $skipValidation = true;
    protected $cleanValidationRules = true;

    protected $validationRules = [
        'exercise_id' => 'permit_empty|numeric',
        'farmer_id' => 'required|numeric',
        'crop_id' => 'required|numeric',
        'block_code' => 'required|max_length[50]',
        'org_id' => 'required|numeric',
        'country_id' => 'required|numeric',
        'province_id' => 'required|numeric',
        'district_id' => 'required|numeric',
        'llg_id' => 'required|numeric',
        'ward_id' => 'required|numeric',
        'village' => 'required|max_length[100]',
        'block_site' => 'required|max_length[200]',
        'lon' => 'permit_empty|max_length[50]',
        'lat' => 'permit_empty|max_length[50]',
        'remarks' => 'permit_empty',
        'status' => 'required|max_length[50]',
        'created_by' => 'required|numeric'
    ];

    protected $validationMessages = [
        'farmer_id' => [
            'required' => 'Farmer is required',
            'numeric' => 'Invalid farmer selection'
        ],
        'crop_id' => [
            'required' => 'Crop is required',
            'numeric' => 'Invalid crop selection'
        ],
        'block_code' => [
            'required' => 'Block code is required',
            'max_length' => 'Block code cannot exceed 50 characters'
        ],
        'village' => [
            'required' => 'Village is required',
            'max_length' => 'Village name cannot exceed 100 characters'
        ],
        'block_site' => [
            'required' => 'Block site is required',
            'max_length' => 'Block site cannot exceed 200 characters'
        ]
    ];

    /**
     * Get farm blocks with detailed information including farmer, crop, and location details
     */
    public function getFarmBlocksWithDetails($district_id = null)
    {
        $builder = $this->db->table($this->table . ' as fb');
        $builder->select('fb.*, f.given_name, f.surname, f.gender, f.farmer_code, c.crop_name, c.crop_color_code, d.name as district_name, l.name as llg_name, w.name as ward_name')
                ->join('farmer_information as f', 'f.id = fb.farmer_id', 'left')
                ->join('adx_crops as c', 'c.id = fb.crop_id', 'left')
                ->join('adx_district as d', 'd.id = fb.district_id', 'left')
                ->join('adx_llg as l', 'l.id = fb.llg_id', 'left')
                ->join('adx_ward as w', 'w.id = fb.ward_id', 'left')
                ->where('fb.status !=', 'deleted');

        if ($district_id) {
            $builder->where('fb.district_id', $district_id);
        }

        $query = $builder->get();
        $results = $query->getResultArray();

        // Keep individual name fields for the view
        foreach ($results as &$result) {
            $result['farmer_name'] = $result['given_name'] . ' ' . $result['surname'];
        }

        return $results;
    }

    /**
     * Get a single farm block with details
     */
    public function getFarmBlockWithDetails($id, $district_id = null)
    {
        $builder = $this->db->table($this->table . ' as fb');
        $builder->select('fb.*, f.given_name, f.surname, f.gender, c.crop_name, c.crop_color_code, d.name as district_name, l.name as llg_name, w.name as ward_name')
                ->join('farmer_information as f', 'f.id = fb.farmer_id', 'left')
                ->join('adx_crops as c', 'c.id = fb.crop_id', 'left')
                ->join('adx_district as d', 'd.id = fb.district_id', 'left')
                ->join('adx_llg as l', 'l.id = fb.llg_id', 'left')
                ->join('adx_ward as w', 'w.id = fb.ward_id', 'left')
                ->where('fb.id', $id)
                ->where('fb.status !=', 'deleted');

        if ($district_id) {
            $builder->where('fb.district_id', $district_id);
        }

        $result = $builder->get()->getRowArray();

        if ($result) {
            $result['farmer_name'] = $result['given_name'] . ' ' . $result['surname'];
        }

        return $result;
    }

    /**
     * Generate unique crops farm block code
     * Format: F8 + province_code + increment (with leading zeros)
     * Example: F81401 (F8 + 14 + 01)
     */
    public function generateBlockCode($provinceId)
    {
        // Get province code
        $provinceModel = new \App\Models\AdxProvinceModel();
        $province = $provinceModel->find($provinceId);

        if (!$province) {
            throw new \Exception('Province not found');
        }

        // Check if provincecode exists and is not empty
        if (empty($province['provincecode'])) {
            // Use province ID as fallback if provincecode is not set
            $provinceCode = str_pad($provinceId, 2, '0', STR_PAD_LEFT);
        } else {
            $provinceCode = $province['provincecode'];
        }

        // Get the next increment number for this province
        $lastBlock = $this->where('province_id', $provinceId)
                          ->where('block_code LIKE', "F8{$provinceCode}%")
                          ->orderBy('block_code', 'DESC')
                          ->first();

        $increment = 1;
        if ($lastBlock) {
            // Extract increment from last block code
            $lastCode = $lastBlock['block_code'];
            $lastIncrement = (int)substr($lastCode, strlen("F8{$provinceCode}"));
            $increment = $lastIncrement + 1;
        }

        // Format increment with leading zeros (minimum 2 digits)
        $formattedIncrement = str_pad($increment, 2, '0', STR_PAD_LEFT);

        // Generate block code: F8 + province_code + increment
        $blockCode = "F8{$provinceCode}{$formattedIncrement}";

        // Ensure uniqueness (in case of race conditions)
        while ($this->where('block_code', $blockCode)->first()) {
            $increment++;
            $formattedIncrement = str_pad($increment, 2, '0', STR_PAD_LEFT);
            $blockCode = "F8{$provinceCode}{$formattedIncrement}";
        }

        return $blockCode;
    }

    /**
     * Soft delete a farm block
     */
    public function softDelete($id, $deleted_by)
    {
        return $this->update($id, [
            'status' => 'deleted',
            'deleted_by' => $deleted_by,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
    }
}
