<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">Disease Data Management</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/crops/diseases') ?>">Disease Data</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Block <?= esc($block['block_code']) ?></li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/crops/diseases') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Disease Data
                    </a>
                    <a href="<?= base_url('staff/crops/diseases/create/' . $block['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Disease Data
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Block Information Cards -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Block Details</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-sm-6">
                            <p><strong>Block Code:</strong><br> <?= esc($block['block_code']) ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p><strong>Crop:</strong><br> <?= esc($crop['crop_name']) ?></p>
                        </div>
                        <div class="col-12">
                            <p><strong>Farmer:</strong><br> <?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></p>
                        </div>
                        <div class="col-12">
                            <p><strong>Remarks:</strong><br> <?= esc($block['remarks']) ?: 'No remarks' ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12 col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-map-marker-alt"></i> Block Location</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-sm-6">
                            <p><strong>Village:</strong><br> <?= esc($block['village']) ?></p>
                        </div>
                        <div class="col-sm-6">
                            <p><strong>Block Site:</strong><br> <?= esc($block['block_site']) ?></p>
                        </div>
                        <div class="col-12">
                            <p><strong>Province:</strong><br> 
                                <?= esc($province['name']) ?>, <?= esc($district['name']) ?>, 
                                <?= esc($llg['name']) ?>, <?= esc($ward['name']) ?>
                            </p>
                        </div>
                        <div class="col-12">
                            <p><strong>Coordinates:</strong><br> <?= esc($block['lon']) ?>, <?= esc($block['lat']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Disease History Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bug me-2"></i>Disease History
                    </h5>
                    <p class="card-text mb-0 text-muted">Disease data records for this farm block</p>
                </div>
                <div class="card-body">
                    <!-- Success/Error Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="diseaseHistoryTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Disease Info</th>
                                    <th>Plants Affected</th>
                                    <th>Area (Ha)</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($diseases_data as $data): ?>
                                <tr>
                                    <td><strong><?= date('d M Y', strtotime($data['action_date'])) ?></strong></td>
                                    <td>
                                        <strong><?= esc($data['disease_name']) ?></strong><br>
                                        <small class="text-muted"><?= esc($data['disease_type_name']) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning"><?= number_format($data['number_of_plants']) ?></span>
                                        <?php if (!empty($data['breed'])): ?>
                                            <br><small class="text-muted"><?= esc($data['breed']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><span class="badge bg-info"><?= number_format($data['hectares'], 2) ?> Ha</span></td>
                                    <td><span class="badge bg-success">Recorded</span></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('staff/crops/diseases/edit/' . $data['id']) ?>" 
                                               class="btn btn-sm btn-warning" title="Edit Disease Data">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="<?= base_url('staff/crops/diseases/delete/' . $data['id']) ?>" 
                                               class="btn btn-sm btn-danger" title="Delete Disease Data"
                                               onclick="return confirm('Are you sure you want to delete this disease record?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    $('#diseaseHistoryTable').DataTable({
        responsive: true,
        order: [[0, 'desc']],
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: -1 }
        ],
        language: {
            search: "Search disease records:",
            lengthMenu: "Show _MENU_ disease records per page",
            info: "Showing _START_ to _END_ of _TOTAL_ disease records",
            infoEmpty: "No disease records found",
            infoFiltered: "(filtered from _MAX_ total disease records)"
        }
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
