<?= $this->extend('templates/staff_template') ?>
<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0"><?= esc($page_header) ?></h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item active">Crops Data</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff') ?>" class="btn btn-secondary me-2">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-seedling me-2"></i>Farm Blocks - Crops Data Management
                    </h5>
                    <p class="card-text mb-0 text-muted"><?= esc($page_desc) ?></p>
                </div>
            <div class="card-body">
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="fas fa-check-circle me-2"></i><?= session()->getFlashdata('success') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="fas fa-exclamation-circle me-2"></i><?= session()->getFlashdata('error') ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if ($farm_blocks): ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="farmBlocksTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Block Code</th>
                                    <th>Farmer</th>
                                    <th>Crop</th>
                                    <th>Location</th>
                                    <th>Ward/LLG</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($farm_blocks as $block): ?>
                                    <tr>
                                        <td>
                                            <strong><?= esc($block['block_code']) ?></strong>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>
                                                    <?php
                                                    foreach ($farmers as $farmer):
                                                        if ($farmer['id'] == $block['farmer_id']):
                                                            echo esc($farmer['given_name'] . ' ' . $farmer['surname']);
                                                        endif;
                                                    endforeach;
                                                    ?>
                                                </strong>
                                            </div>
                                            <small class="text-muted">Farmer ID: <?= esc($block['farmer_id']) ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">
                                                <?php
                                                foreach ($crops as $crop):
                                                    if ($crop['id'] == $block['crop_id']):
                                                        echo esc($crop['crop_name']);
                                                    endif;
                                                endforeach;
                                                ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?= esc($block['village']) ?> - <?= esc($block['block_site']) ?>
                                        </td>
                                        <td>
                                            <div>
                                                <strong><?= esc($block['ward_name']) ?></strong>
                                            </div>
                                            <small class="text-muted"><?= esc($block['llg_name']) ?></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-<?= $block['status'] == 'active' ? 'success' : 'secondary' ?>">
                                                <?= ucfirst(esc($block['status'])) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?= base_url('staff/crops/data-show/' . $block['id']) ?>"
                                                   class="btn btn-sm btn-info"
                                                   title="View Crops Data">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= base_url('staff/crops/data-create/' . $block['id']) ?>"
                                                   class="btn btn-sm btn-warning"
                                                   title="Add Crops Data">
                                                    <i class="fas fa-plus"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-seedling fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Farm Blocks Found</h5>
                        <p class="text-muted">No active farm blocks found for your district. Please create farm blocks first.</p>
                        <a href="<?= base_url('staff/crops-farm-blocks') ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Manage Farm Blocks
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#farmBlocksTable').DataTable({
        responsive: true,
        order: [[0, 'desc']],
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: [6] } // Disable sorting on Actions column
        ],
        language: {
            search: "Search farm blocks:",
            lengthMenu: "Show _MENU_ farm blocks per page",
            info: "Showing _START_ to _END_ of _TOTAL_ farm blocks",
            infoEmpty: "No farm blocks found",
            infoFiltered: "(filtered from _MAX_ total farm blocks)"
        }
    });
});
</script>

<?= $this->endSection() ?>
