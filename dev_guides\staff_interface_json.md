# Staff Interface Design Pattern - JSON Documentation

## Overview
This document outlines the consistent interface design pattern used across the AgriStats staff portal, based on the crop-buyers page design.

## Interface Structure JSON

```json
{
  "page_layout": {
    "container": "container-fluid",
    "structure": [
      {
        "section": "page_header",
        "classes": "row mb-3",
        "content": {
          "left_side": {
            "title": "h4 mb-0",
            "breadcrumb": {
              "nav": "nav aria-label='breadcrumb'",
              "ol": "breadcrumb mb-0",
              "items": [
                {"text": "Dashboard", "link": "base_url('staff')", "active": false},
                {"text": "Current Page", "link": null, "active": true}
              ]
            }
          },
          "right_side": {
            "buttons": [
              {"text": "Back to Dashboard", "class": "btn btn-secondary me-2", "icon": "fas fa-arrow-left"},
              {"text": "Add New Item", "class": "btn btn-primary", "icon": "fas fa-plus"}
            ]
          }
        }
      },
      {
        "section": "main_content",
        "classes": "row",
        "content": {
          "card": {
            "classes": "card",
            "header": {
              "title": "h5 card-title mb-0",
              "icon": "fas fa-[relevant-icon] me-2",
              "description": "card-text mb-0 text-muted"
            },
            "body": {
              "alerts": {
                "success": "alert alert-success alert-dismissible fade show",
                "error": "alert alert-danger alert-dismissible fade show"
              },
              "table": {
                "wrapper": "table-responsive",
                "classes": "table table-striped table-hover",
                "id": "[tableName]Table",
                "thead": "table-dark",
                "structure": "standard_data_table"
              }
            }
          }
        }
      }
    ]
  },
  "table_design": {
    "header": {
      "background": "table-dark",
      "columns": [
        {"name": "Primary ID/Code", "sortable": true},
        {"name": "Main Entity Name", "sortable": true},
        {"name": "Category/Type", "sortable": true, "badge": true},
        {"name": "Location/Contact", "sortable": true},
        {"name": "Secondary Info", "sortable": true},
        {"name": "Status", "sortable": true, "badge": true},
        {"name": "Actions", "sortable": false}
      ]
    },
    "body": {
      "row_structure": {
        "primary_column": {
          "format": "strong text",
          "example": "<strong>BLOCK001</strong>"
        },
        "name_column": {
          "format": "strong name + small subtitle",
          "example": "<strong>John Doe</strong><br><small class='text-muted'>ID: 123</small>"
        },
        "badge_columns": {
          "success": "badge bg-success",
          "info": "badge bg-info", 
          "warning": "badge bg-warning",
          "secondary": "badge bg-secondary"
        },
        "status_column": {
          "active": "badge bg-success",
          "inactive": "badge bg-secondary"
        },
        "actions_column": {
          "button_group": "btn-group",
          "buttons": [
            {"type": "view", "class": "btn btn-sm btn-info", "icon": "fas fa-eye"},
            {"type": "edit", "class": "btn btn-sm btn-warning", "icon": "fas fa-edit"},
            {"type": "delete", "class": "btn btn-sm btn-danger", "icon": "fas fa-trash"}
          ]
        }
      }
    }
  },
  "datatable_config": {
    "responsive": true,
    "order": [[0, "desc"]],
    "pageLength": 25,
    "columnDefs": [
      {"orderable": false, "targets": "last_column_index"}
    ],
    "language": {
      "search": "Search [entity_name]:",
      "lengthMenu": "Show _MENU_ [entity_name] per page",
      "info": "Showing _START_ to _END_ of _TOTAL_ [entity_name]",
      "infoEmpty": "No [entity_name] found",
      "infoFiltered": "(filtered from _MAX_ total [entity_name])"
    }
  },
  "color_scheme": {
    "primary_colors": {
      "green": "#2e7d32",
      "dark_green": "#1b5e20", 
      "light_green": "#4caf50"
    },
    "button_colors": {
      "primary": "btn-primary",
      "secondary": "btn-secondary", 
      "success": "btn-success",
      "info": "btn-info",
      "warning": "btn-warning",
      "danger": "btn-danger"
    },
    "badge_colors": {
      "active": "bg-success",
      "inactive": "bg-secondary",
      "category": "bg-success",
      "status_good": "bg-info",
      "status_warning": "bg-warning"
    }
  },
  "typography": {
    "font_family": "Poppins, sans-serif",
    "headings": {
      "page_title": "h4 mb-0",
      "card_title": "h5 card-title mb-0",
      "section_title": "h6"
    },
    "text_styles": {
      "muted": "text-muted",
      "strong": "strong",
      "small": "small"
    }
  },
  "spacing": {
    "page_margins": "mb-3",
    "card_spacing": "row",
    "button_spacing": "me-2",
    "icon_spacing": "me-2"
  },
  "responsive_design": {
    "breakpoints": {
      "mobile": "d-md-none",
      "desktop": "d-none d-md-block"
    },
    "table": "table-responsive",
    "buttons": "btn-group"
  }
}
```

## Implementation Guidelines

### 1. Page Header Pattern
- Always use breadcrumb navigation
- Right-aligned action buttons
- Consistent spacing with `mb-3`

### 2. Card Structure
- Use `card` class for main content
- Header with title and description
- Body with table or form content

### 3. Table Design
- Dark header (`table-dark`)
- Striped and hoverable rows
- Consistent column structure
- Badge usage for categories and status

### 4. Button Patterns
- Info buttons for view actions
- Warning buttons for edit actions  
- Danger buttons for delete actions
- Primary buttons for main actions

### 5. DataTable Configuration
- Responsive design enabled
- Consistent language settings
- Proper column sorting configuration

## Usage Examples

### Basic Page Structure
```php
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <!-- Left side: Title + Breadcrumb -->
                <!-- Right side: Action buttons -->
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <!-- Card header and body -->
            </div>
        </div>
    </div>
</div>
```

### Table Structure
```php
<table class="table table-striped table-hover" id="entityTable">
    <thead class="table-dark">
        <!-- Column headers -->
    </thead>
    <tbody>
        <!-- Data rows with badges and buttons -->
    </tbody>
</table>
```

This pattern ensures consistency across all staff portal pages and provides a professional, user-friendly interface.
