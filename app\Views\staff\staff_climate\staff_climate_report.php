<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-gray-800"><?= $page_header ?></h1>
                    <p class="mb-0 text-muted"><?= $page_desc ?></p>
                </div>
                <div>
                    <a href="<?= base_url('staff/tools/climate-data/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Climate Focus Location
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Weather Data Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-cloud-sun me-2"></i>Climate Data Parameters
                    </h6>
                </div>
                <div class="card-body">
                    <form id="weatherForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="gps" class="form-label">GPS Coordinates (latitude,longitude) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="gps" name="gps" 
                                       placeholder="e.g., -6.314993, 143.95555" required>
                                <div class="form-text">Enter GPS coordinates for the location you want to analyze</div>
                            </div>
                            <div class="col-md-6">
                                <label for="monthsAgo" class="form-label">Number of Months <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="monthsAgo" name="monthsAgo" 
                                       min="1" max="24" value="9" required>
                                <div class="form-text">Number of months of historical data to fetch (default: 9 months)</div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary" id="submitButton">
                                    <span class="button-text">
                                        <i class="fas fa-search me-2"></i>Get Climate Data
                                    </span>
                                    <div class="spinner-border spinner-border-sm d-none" role="status" id="loadingSpinner">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </button>
                            </div>
                        </div>
                        <div class="alert alert-danger d-none mt-3" id="errorMessage" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span id="errorText"></span>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div id="resultsSection" class="d-none">
        <!-- Location Map Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-map-marker-alt me-2"></i>Location Map
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="map" style="height: 400px; width: 100%;"></div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                <span id="locationInfo">Map will show the location of entered GPS coordinates</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Climate Summary Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="fas fa-chart-line me-2"></i>Climate Data Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row" id="overallSummary">
                            <!-- Overall summary will be populated here -->
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 1 -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Temperature Trends</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="temperatureChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Humidity Levels</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="humidityChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 2 -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Precipitation</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="precipitationChart" height="300"></canvas>
                    </div>
                </div>
            </div>
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Wind Speed</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="windspeedChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row 3 -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Sunshine Duration</h6>
                    </div>
                    <div class="card-body">
                        <canvas id="sunshineChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">Weather Data Table</h6>
                        <button class="btn btn-success btn-sm" id="copyButton">
                            <i class="fas fa-copy me-2"></i>Copy Table Data
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="weatherTable">
                                <thead class="table-primary">
                                    <tr>
                                        <th>Date & Time</th>
                                        <th>Weather</th>
                                        <th>Temperature (°C)</th>
                                        <th>Humidity (%)</th>
                                        <th>Precipitation (mm)</th>
                                        <th>Wind Speed (km/h)</th>
                                        <th>Sunshine Duration (hours)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Rows will be populated dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include Leaflet CSS and JS for OpenStreetMap -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin=""/>
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
        integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Weather code descriptions
const weatherCodeDescriptions = {
    0: "Clear sky", 1: "Mainly clear", 2: "Partly cloudy", 3: "Overcast",
    45: "Fog", 48: "Depositing rime fog", 51: "Light drizzle", 53: "Moderate drizzle",
    55: "Dense drizzle", 56: "Light freezing drizzle", 57: "Dense freezing drizzle",
    61: "Slight rain", 63: "Moderate rain", 65: "Heavy rain", 66: "Light freezing rain",
    67: "Heavy freezing rain", 71: "Slight snow fall", 73: "Moderate snow fall",
    75: "Heavy snow fall", 77: "Snow grains", 80: "Slight rain showers",
    81: "Moderate rain showers", 82: "Violent rain showers", 85: "Slight snow showers",
    86: "Heavy snow showers", 95: "Thunderstorm", 96: "Thunderstorm with slight hail",
    99: "Thunderstorm with heavy hail"
};

// Initialize chart instances and map
let temperatureChart, humidityChart, precipitationChart, windspeedChart, sunshineChart;
let map, marker;

document.getElementById('weatherForm').addEventListener('submit', function(event) {
    event.preventDefault();
    
    // Set loading state
    const submitButton = document.getElementById('submitButton');
    const buttonText = submitButton.querySelector('.button-text');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const errorMessage = document.getElementById('errorMessage');
    
    submitButton.disabled = true;
    buttonText.classList.add('d-none');
    loadingSpinner.classList.remove('d-none');
    errorMessage.classList.add('d-none');
    
    const gps = document.getElementById('gps').value;
    const monthsAgo = parseInt(document.getElementById('monthsAgo').value);
    
    const [latitude, longitude] = gps.split(',').map(coord => parseFloat(coord.trim()));
    
    if (isNaN(latitude) || isNaN(longitude) || isNaN(monthsAgo)) {
        showError('Please enter valid GPS coordinates and number of months.');
        resetButton();
        return;
    }
    
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(endDate.getMonth() - monthsAgo);
    
    const formatDate = (date) => date.toISOString().split('T')[0];
    
    const url = `https://archive-api.open-meteo.com/v1/archive?latitude=${latitude}&longitude=${longitude}&start_date=${formatDate(startDate)}&end_date=${formatDate(endDate)}&hourly=weather_code,temperature_2m,relative_humidity_2m,precipitation,windspeed_10m,sunshine_duration`;

    console.log('API URL:', url);
    console.log('Date range:', formatDate(startDate), 'to', formatDate(endDate));

    fetch(url)
        .then(response => response.json())
        .then(data => {
            console.log('Raw API response:', data);
            if (data.error) {
                showError(`Error: ${data.reason}`);
            } else {
                const hourly = data.hourly;
                
                // Extract data for charts
                const rawTimes = hourly.time; // Keep original time data for summary calculations
                const labels = hourly.time.map(time => new Date(time).toLocaleString());
                const temperatures = hourly.temperature_2m;
                const humidities = hourly.relative_humidity_2m;
                const precipitations = hourly.precipitation;
                const windspeeds = hourly.windspeed_10m;
                // Check if sunshine_duration exists in the response
                let sunshineDurations = [];
                if (hourly.sunshine_duration && Array.isArray(hourly.sunshine_duration)) {
                    sunshineDurations = hourly.sunshine_duration.map(duration => {
                        // Handle null, undefined, or invalid values
                        if (duration === null || duration === undefined || isNaN(duration) || duration < 0) {
                            return 0;
                        }
                        // Convert from seconds to hours and round to 2 decimal places
                        const hours = duration / 3600;
                        return parseFloat(hours.toFixed(2));
                    });
                } else {
                    console.warn('Sunshine duration data not available in API response');
                    // Create array of zeros with same length as other data
                    sunshineDurations = new Array(hourly.time.length).fill(0);
                }

                // Debug: Log raw and processed sunshine data
                console.log('Raw sunshine duration available:', !!hourly.sunshine_duration);
                console.log('Raw sunshine duration sample (first 10):', hourly.sunshine_duration ? hourly.sunshine_duration.slice(0, 10) : 'N/A');
                console.log('Processed sunshine duration sample (first 10):', sunshineDurations.slice(0, 10));
                console.log('Total sunshine duration entries:', sunshineDurations.length);
                console.log('Non-zero sunshine entries:', sunshineDurations.filter(d => d > 0).length);
                const weatherCodes = hourly.weather_code;

                // Destroy existing charts if they exist
                if (temperatureChart) temperatureChart.destroy();
                if (humidityChart) humidityChart.destroy();
                if (precipitationChart) precipitationChart.destroy();
                if (windspeedChart) windspeedChart.destroy();
                if (sunshineChart) sunshineChart.destroy();

                // Show results section
                document.getElementById('resultsSection').classList.remove('d-none');

                // Initialize and update map
                initializeMap(latitude, longitude);

                // Create charts
                createCharts(labels, temperatures, humidities, precipitations, windspeeds, sunshineDurations);

                // Generate and display summary
                generateSummary(temperatures, humidities, precipitations, windspeeds, sunshineDurations);

                // Populate table
                populateTable(labels, weatherCodes, temperatures, humidities, precipitations, windspeeds, sunshineDurations);
            }
        })
        .catch(error => {
            console.error('Error fetching weather data:', error);
            showError('Failed to fetch weather data. Please try again.');
        })
        .finally(() => {
            resetButton();
        });
});

function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    errorText.textContent = message;
    errorMessage.classList.remove('d-none');
}

function resetButton() {
    const submitButton = document.getElementById('submitButton');
    const buttonText = submitButton.querySelector('.button-text');
    const loadingSpinner = document.getElementById('loadingSpinner');

    submitButton.disabled = false;
    buttonText.classList.remove('d-none');
    loadingSpinner.classList.add('d-none');
}

function initializeMap(latitude, longitude) {
    // Initialize map if it doesn't exist
    if (!map) {
        map = L.map('map').setView([latitude, longitude], 10);

        // Add OpenStreetMap tiles
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18
        }).addTo(map);
    } else {
        // Update existing map view
        map.setView([latitude, longitude], 10);
    }

    // Remove existing marker if any
    if (marker) {
        map.removeLayer(marker);
    }

    // Add new marker
    marker = L.marker([latitude, longitude]).addTo(map)
        .bindPopup(`<b>Climate Data Location</b><br>Coordinates: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`)
        .openPopup();

    // Update location info
    document.getElementById('locationInfo').innerHTML =
        `<i class="fas fa-map-marker-alt me-1"></i>Location: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`;

    // Ensure map renders properly
    setTimeout(() => {
        map.invalidateSize();
    }, 100);
}

function generateSummary(temperatures, humidities, precipitations, windspeeds, sunshineDurations) {
    // Enhanced debug sunshine data
    console.log('=== SUNSHINE DURATION DEBUG ===');
    console.log('Total sunshine entries:', sunshineDurations.length);
    console.log('Sunshine durations sample (first 20):', sunshineDurations.slice(0, 20));
    console.log('Sunshine durations type check:', sunshineDurations.map(d => typeof d).slice(0, 10));
    console.log('Non-zero sunshine values:', sunshineDurations.filter(d => d > 0));
    console.log('Max sunshine value in dataset:', Math.max(...sunshineDurations.filter(d => !isNaN(d) && isFinite(d))));
    console.log('Min sunshine value in dataset:', Math.min(...sunshineDurations.filter(d => !isNaN(d) && isFinite(d))));

    // Calculate overall statistics
    console.log('=== CALCULATING STATISTICS ===');
    const tempStats = calculateStats(temperatures);
    const humidityStats = calculateStats(humidities);
    const precipStats = calculateStats(precipitations);
    const windStats = calculateStats(windspeeds);
    const sunshineStats = calculateStats(sunshineDurations);

    console.log('Final sunshine stats:', sunshineStats);
    console.log('=== END DEBUG ===');

    // Display overall summary
    const overallSummary = document.getElementById('overallSummary');
    overallSummary.innerHTML = `
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-danger h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Temperature</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${tempStats.avg.toFixed(1)}°C</div>
                    <div class="text-xs text-gray-600">High: ${tempStats.max.toFixed(1)}°C | Low: ${tempStats.min.toFixed(1)}°C</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-info h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Humidity</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${humidityStats.avg.toFixed(1)}%</div>
                    <div class="text-xs text-gray-600">High: ${humidityStats.max.toFixed(1)}% | Low: ${humidityStats.min.toFixed(1)}%</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-success h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Precipitation</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${precipStats.avg.toFixed(1)}mm</div>
                    <div class="text-xs text-gray-600">Max: ${precipStats.max.toFixed(1)}mm | Min: ${precipStats.min.toFixed(1)}mm</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-warning h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Wind Speed</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${windStats.avg.toFixed(1)} km/h</div>
                    <div class="text-xs text-gray-600">Max: ${windStats.max.toFixed(1)} km/h | Min: ${windStats.min.toFixed(1)} km/h</div>
                </div>
            </div>
        </div>
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card border-left-primary h-100">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Sunshine Duration</div>
                    <div class="h6 mb-0 font-weight-bold text-gray-800">Avg: ${sunshineStats.avg.toFixed(1)} hrs</div>
                    <div class="text-xs text-gray-600">Max: ${sunshineStats.max.toFixed(1)} hrs | Min: ${sunshineStats.min.toFixed(1)} hrs</div>
                </div>
            </div>
        </div>
    `;


}

function calculateStats(data) {
    // Convert all values to numbers and filter out invalid ones
    const validData = data
        .map(val => {
            if (typeof val === 'string') {
                return parseFloat(val);
            }
            return val;
        })
        .filter(val => val !== null && val !== undefined && !isNaN(val) && isFinite(val) && val >= 0);

    console.log(`calculateStats: Total entries: ${data.length}, Valid entries: ${validData.length}`);

    if (validData.length === 0) {
        console.log('calculateStats: No valid data found, returning zeros');
        return { avg: 0, min: 0, max: 0, sum: 0 };
    }

    const sum = validData.reduce((a, b) => a + b, 0);
    const avg = sum / validData.length;
    const min = Math.min(...validData);
    const max = Math.max(...validData);

    console.log(`calculateStats: Sum: ${sum}, Avg: ${avg}, Min: ${min}, Max: ${max}`);
    return { avg, min, max, sum };
}



function createCharts(labels, temperatures, humidities, precipitations, windspeeds, sunshineDurations) {
    // Temperature Chart
    temperatureChart = new Chart(document.getElementById('temperatureChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Temperature (°C)',
                data: temperatures,
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Temperature (°C)' } }
            }
        }
    });

    // Humidity Chart
    humidityChart = new Chart(document.getElementById('humidityChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Humidity (%)',
                data: humidities,
                borderColor: 'rgba(54, 162, 235, 1)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Humidity (%)' } }
            }
        }
    });

    // Precipitation Chart
    precipitationChart = new Chart(document.getElementById('precipitationChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Precipitation (mm)',
                data: precipitations,
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Precipitation (mm)' } }
            }
        }
    });

    // Wind Speed Chart
    windspeedChart = new Chart(document.getElementById('windspeedChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Wind Speed (km/h)',
                data: windspeeds,
                borderColor: 'rgba(153, 102, 255, 1)',
                backgroundColor: 'rgba(153, 102, 255, 0.1)',
                fill: false,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Wind Speed (km/h)' } }
            }
        }
    });

    // Sunshine Duration Chart
    sunshineChart = new Chart(document.getElementById('sunshineChart'), {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Sunshine Duration (hours)',
                data: sunshineDurations,
                borderColor: 'rgba(255, 205, 86, 1)',
                backgroundColor: 'rgba(255, 205, 86, 0.2)',
                fill: true,
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: { legend: { display: false } },
            scales: {
                x: { display: true, title: { display: true, text: 'Date & Time' } },
                y: { display: true, title: { display: true, text: 'Sunshine Duration (hours)' }, min: 0 }
            }
        }
    });
}

function populateTable(labels, weatherCodes, temperatures, humidities, precipitations, windspeeds, sunshineDurations) {
    const tableBody = document.querySelector('#weatherTable tbody');
    tableBody.innerHTML = ''; // Clear previous data

    for (let i = 0; i < labels.length; i++) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${labels[i]}</td>
            <td>${weatherCodeDescriptions[weatherCodes[i]] || 'Unknown'} (${weatherCodes[i]})</td>
            <td>${temperatures[i]}</td>
            <td>${humidities[i]}</td>
            <td>${precipitations[i]}</td>
            <td>${windspeeds[i]}</td>
            <td>${sunshineDurations[i]}</td>
        `;
        tableBody.appendChild(row);
    }
}

// Copy Table Data to Clipboard
document.getElementById('copyButton').addEventListener('click', function() {
    const table = document.getElementById('weatherTable');
    let text = '';

    // Extract table headers
    const headers = Array.from(table.querySelectorAll('th')).map(th => th.innerText);
    text += headers.join('\t') + '\n';

    // Extract table rows
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const rowData = Array.from(row.querySelectorAll('td')).map(td => td.innerText);
        text += rowData.join('\t') + '\n';
    });

    // Copy to clipboard
    navigator.clipboard.writeText(text)
        .then(() => {
            // Show success message
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
            this.classList.remove('btn-success');
            this.classList.add('btn-success');

            setTimeout(() => {
                this.innerHTML = originalText;
            }, 2000);
        })
        .catch(() => {
            alert('Failed to copy table data.');
        });
});
</script>
<?= $this->endSection() ?>
