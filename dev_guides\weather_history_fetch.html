<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Historical Weather Data</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap"
      rel="stylesheet"
    />
    <style>
      :root {
        --primary-color: #1a73e8;
        --secondary-color: #f1f3f4;
        --background-color: #ffffff;
        --text-color: #202124;
        --border-color: #dadce0;
        --chart-colors: [ "#1a73e8", "#34a853", "#fbbc05", "#ea4335"];
      }

      body {
        font-family: "Roboto", Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: var(--background-color);
        color: var(--text-color);
        line-height: 1.6;
      }

      h1 {
        color: var(--primary-color);
        text-align: center;
        margin-bottom: 2rem;
        font-weight: 500;
      }

      #weatherForm {
        max-width: 600px;
        margin: 0 auto 2rem;
        padding: 2rem;
        background: var(--secondary-color);
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      label {
        font-weight: 500;
        margin-bottom: 0.5rem;
        display: block;
        color: var(--text-color);
      }

      input {
        width: 100%;
        padding: 0.75rem;
        margin-bottom: 1rem;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 1rem;
        transition: border-color 0.3s ease;
      }

      input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
      }

      button {
        background-color: var(--primary-color);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 4px;
        font-size: 1rem;
        cursor: pointer;
        transition: background-color 0.3s ease;
        width: 100%;
        margin-top: 1rem;
      }

      button:hover {
        background-color: #1557b7;
      }

      #result {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 0 1rem;
      }

      .chart-container {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
      }

      canvas {
        max-width: 100%;
        height: 400px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin: 2rem 0;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      th,
      td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }

      th {
        background-color: var(--primary-color);
        color: white;
        font-weight: 500;
      }

      tr:hover {
        background-color: rgba(241, 243, 244, 0.5);
      }

      #copyButton {
        margin: 1rem 0;
        background-color: #34a853;
      }

      #copyButton:hover {
        background-color: #2a8a44;
      }

      .spinner {
        display: none;
        border: 4px solid #f3f3f3;
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        width: 30px;
        height: 30px;
        animation: spin 1s linear infinite;
        margin: 20px auto;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .loading #submitButton {
        background-color: #ccc;
        cursor: not-allowed;
      }

      .loading .spinner {
        display: block;
      }

      .error-message {
        color: #dc3545;
        background-color: #f8d7da;
        padding: 10px;
        border-radius: 4px;
        margin: 1rem 0;
        display: none;
      }

      @media (max-width: 768px) {
        body {
          padding: 10px;
        }

        #weatherForm {
          padding: 1rem;
        }

        .chart-container {
          padding: 1rem;
        }

        canvas {
          height: 300px;
        }

        th,
        td {
          padding: 8px 10px;
        }
      }
    </style>
    <!-- Include Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  </head>
  <body>
    <h1>Historical Weather Data</h1>
    <form id="weatherForm">
      <label for="gps">GPS Coordinates (latitude,longitude):</label>
      <input
        type="text"
        id="gps"
        name="gps"
        placeholder="e.g., 52.52,13.41"
        required
      />

      <label for="monthsAgo">Number of Months Ago:</label>
      <input type="number" id="monthsAgo" name="monthsAgo" min="1" required />

      <button type="submit" id="submitButton">
        <span class="button-text">Get Weather Data</span>
        <div class="spinner"></div>
      </button>
      <div class="error-message" id="errorMessage"></div>
    </form>

    <div id="result">
      <div class="chart-container">
        <canvas id="temperatureChart"></canvas>
      </div>
      <div class="chart-container">
        <canvas id="humidityChart"></canvas>
      </div>
      <div class="chart-container">
        <canvas id="precipitationChart"></canvas>
      </div>
      <div class="chart-container">
        <canvas id="windspeedChart"></canvas>
      </div>
      <div class="chart-container">
        <canvas id="sunshineChart"></canvas>
      </div>

      <!-- Table for displaying weather data -->
      <h2>Weather Data Table</h2>

      <!-- Copy Button -->
      <button id="copyButton">Copy Table Data</button>

      <table id="weatherTable">
        <thead>
          <tr>
            <th>Date & Time</th>
            <th>Weather</th>
            <th>Temperature (°C)</th>
            <th>Humidity (%)</th>
            <th>Precipitation (mm)</th>
            <th>Wind Speed (km/h)</th>
            <th>Sunshine Duration (hours)</th>
          </tr>
        </thead>
        <tbody>
          <!-- Rows will be populated dynamically -->
        </tbody>
      </table>
    </div>

    <script>
      // Weather code descriptions
      const weatherCodeDescriptions = {
        0: "Clear sky",
        1: "Mainly clear",
        2: "Partly cloudy",
        3: "Overcast",
        45: "Fog",
        48: "Depositing rime fog",
        51: "Light drizzle",
        53: "Moderate drizzle",
        55: "Dense drizzle",
        56: "Light freezing drizzle",
        57: "Dense freezing drizzle",
        61: "Slight rain",
        63: "Moderate rain",
        65: "Heavy rain",
        66: "Light freezing rain",
        67: "Heavy freezing rain",
        71: "Slight snow fall",
        73: "Moderate snow fall",
        75: "Heavy snow fall",
        77: "Snow grains",
        80: "Slight rain showers",
        81: "Moderate rain showers",
        82: "Violent rain showers",
        85: "Slight snow showers",
        86: "Heavy snow showers",
        95: "Thunderstorm",
        96: "Thunderstorm with slight hail",
        99: "Thunderstorm with heavy hail",
      };

      // Initialize chart instances
      let temperatureChart, humidityChart, precipitationChart, windspeedChart, sunshineChart;

      document
        .getElementById("weatherForm")
        .addEventListener("submit", function (event) {
          event.preventDefault();

          // Set loading state
          document.body.classList.add("loading");
          document.getElementById("errorMessage").style.display = "none";

          const gps = document.getElementById("gps").value;
          const monthsAgo = parseInt(
            document.getElementById("monthsAgo").value
          );

          const [latitude, longitude] = gps
            .split(",")
            .map((coord) => parseFloat(coord.trim()));

          if (isNaN(latitude) || isNaN(longitude) || isNaN(monthsAgo)) {
            alert("Please enter valid GPS coordinates and number of months.");
            return;
          }

          const endDate = new Date();
          const startDate = new Date();
          startDate.setMonth(endDate.getMonth() - monthsAgo);

          const formatDate = (date) => date.toISOString().split("T")[0];

          const url = `https://archive-api.open-meteo.com/v1/archive?latitude=${latitude}&longitude=${longitude}&start_date=${formatDate(
            startDate
          )}&end_date=${formatDate(
            endDate
          )}&hourly=weather_code,temperature_2m,relative_humidity_2m,precipitation,windspeed_10m,sunshine_duration`;

          fetch(url)
            .then((response) => response.json())
            .then((data) => {
              const resultDiv = document.getElementById("result");
              if (data.error) {
                resultDiv.textContent = `Error: ${data.reason}`;
              } else {
                const hourly = data.hourly;

                // Extract data for charts
                const labels = hourly.time.map((time) =>
                  new Date(time).toLocaleString()
                );
                const temperatures = hourly.temperature_2m;
                const humidities = hourly.relative_humidity_2m;
                const precipitations = hourly.precipitation;
                const windspeeds = hourly.windspeed_10m;
                const sunshineDurations = hourly.sunshine_duration.map(duration => 
                  duration ? (duration / 3600).toFixed(2) : 0 // Convert seconds to hours
                );
                const weatherCodes = hourly.weather_code;

                // Destroy existing charts if they exist
                if (temperatureChart) temperatureChart.destroy();
                if (humidityChart) humidityChart.destroy();
                if (precipitationChart) precipitationChart.destroy();
                if (windspeedChart) windspeedChart.destroy();
                if (sunshineChart) sunshineChart.destroy();

                // Render Temperature Chart
                temperatureChart = new Chart(
                  document.getElementById("temperatureChart"),
                  {
                    type: "line",
                    data: {
                      labels: labels,
                      datasets: [
                        {
                          label: "Temperature (°C)",
                          data: temperatures,
                          borderColor: "rgba(255, 99, 132, 1)",
                          fill: false,
                        },
                      ],
                    },
                    options: {
                      responsive: true,
                      scales: {
                        x: {
                          display: true,
                          title: {
                            display: true,
                            text: "Date & Time",
                          },
                        },
                        y: {
                          display: true,
                          title: {
                            display: true,
                            text: "Temperature (°C)",
                          },
                        },
                      },
                    },
                  }
                );

                // Render Humidity Chart
                humidityChart = new Chart(
                  document.getElementById("humidityChart"),
                  {
                    type: "line",
                    data: {
                      labels: labels,
                      datasets: [
                        {
                          label: "Humidity (%)",
                          data: humidities,
                          borderColor: "rgba(54, 162, 235, 1)",
                          fill: false,
                        },
                      ],
                    },
                    options: {
                      responsive: true,
                      scales: {
                        x: {
                          display: true,
                          title: {
                            display: true,
                            text: "Date & Time",
                          },
                        },
                        y: {
                          display: true,
                          title: {
                            display: true,
                            text: "Humidity (%)",
                          },
                        },
                      },
                    },
                  }
                );

                // Render Precipitation Chart
                precipitationChart = new Chart(
                  document.getElementById("precipitationChart"),
                  {
                    type: "line",
                    data: {
                      labels: labels,
                      datasets: [
                        {
                          label: "Precipitation (mm)",
                          data: precipitations,
                          borderColor: "rgba(75, 192, 192, 1)",
                          fill: false,
                        },
                      ],
                    },
                    options: {
                      responsive: true,
                      scales: {
                        x: {
                          display: true,
                          title: {
                            display: true,
                            text: "Date & Time",
                          },
                        },
                        y: {
                          display: true,
                          title: {
                            display: true,
                            text: "Precipitation (mm)",
                          },
                        },
                      },
                    },
                  }
                );

                // Render Wind Speed Chart
                windspeedChart = new Chart(
                  document.getElementById("windspeedChart"),
                  {
                    type: "line",
                    data: {
                      labels: labels,
                      datasets: [
                        {
                          label: "Wind Speed (km/h)",
                          data: windspeeds,
                          borderColor: "rgba(153, 102, 255, 1)",
                          fill: false,
                        },
                      ],
                    },
                    options: {
                      responsive: true,
                      scales: {
                        x: {
                          display: true,
                          title: {
                            display: true,
                            text: "Date & Time",
                          },
                        },
                        y: {
                          display: true,
                          title: {
                            display: true,
                            text: "Wind Speed (km/h)",
                          },
                        },
                      },
                    },
                  }
                );

                // Render Sunshine Duration Chart
                sunshineChart = new Chart(
                  document.getElementById("sunshineChart"),
                  {
                    type: "line",
                    data: {
                      labels: labels,
                      datasets: [
                        {
                          label: "Sunshine Duration (hours)",
                          data: sunshineDurations,
                          borderColor: "rgba(255, 205, 86, 1)",
                          backgroundColor: "rgba(255, 205, 86, 0.2)",
                          fill: true,
                        },
                      ],
                    },
                    options: {
                      responsive: true,
                      scales: {
                        x: {
                          display: true,
                          title: {
                            display: true,
                            text: "Date & Time",
                          },
                        },
                        y: {
                          display: true,
                          title: {
                            display: true,
                            text: "Sunshine Duration (hours)",
                          },
                          min: 0,
                        },
                      },
                    },
                  }
                );

                // Populate the table
                const tableBody = document.querySelector("#weatherTable tbody");
                tableBody.innerHTML = ""; // Clear previous data
                for (let i = 0; i < labels.length; i++) {
                  const row = document.createElement("tr");
                  row.innerHTML = `
                                <td>${labels[i]}</td>
                                <td>${
                                  weatherCodeDescriptions[weatherCodes[i]] ||
                                  "Unknown"
                                } (${weatherCodes[i]})</td>
                                <td>${temperatures[i]}</td>
                                <td>${humidities[i]}</td>
                                <td>${precipitations[i]}</td>
                                <td>${windspeeds[i]}</td>
                                <td>${sunshineDurations[i]}</td>
                            `;
                  tableBody.appendChild(row);
                }
              }
            })
            .catch((error) => {
              console.error("Error fetching weather data:", error);
              document.getElementById("errorMessage").textContent =
                "Failed to fetch weather data. Please try again.";
              document.getElementById("errorMessage").style.display = "block";
            })
            .finally(() => {
              document.body.classList.remove("loading");
            });
        });

      // Copy Table Data to Clipboard
      document
        .getElementById("copyButton")
        .addEventListener("click", function () {
          const table = document.getElementById("weatherTable");
          let text = "";

          // Extract table headers
          const headers = Array.from(table.querySelectorAll("th")).map(
            (th) => th.innerText
          );
          text += headers.join("\t") + "\n";

          // Extract table rows
          const rows = table.querySelectorAll("tbody tr");
          rows.forEach((row) => {
            const rowData = Array.from(row.querySelectorAll("td")).map(
              (td) => td.innerText
            );
            text += rowData.join("\t") + "\n";
          });

          // Copy to clipboard
          navigator.clipboard
            .writeText(text)
            .then(() => alert("Table data copied to clipboard!"))
            .catch(() => alert("Failed to copy table data."));
        });
    </script>
  </body>
</html>
