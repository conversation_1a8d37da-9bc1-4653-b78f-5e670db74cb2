<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>

<div class="container-fluid">
    <!-- Breadcrumbs -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('staff/dashboard') ?>">Dashboard</a></li>
            <li class="breadcrumb-item">Reports</li>
            <li class="breadcrumb-item active" aria-current="page">Farmers Report</li>
        </ol>
    </nav>

    <!-- Summary Information -->
    <div class="row g-3 mb-4">
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['total_farmers']) ?></h4>
                            <p class="mb-0">Total Farmers</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['total_crop_blocks']) ?></h4>
                            <p class="mb-0">Crop Blocks</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-seedling"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['total_livestock_blocks']) ?></h4>
                            <p class="mb-0">Livestock Blocks</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-cow"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="mb-0"><?= number_format($stats['farmers_with_email']) ?></h4>
                            <p class="mb-0">With Email</p>
                        </div>
                        <div class="fs-1">
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Farmers Table -->
    <div class="card">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-users me-2"></i>Farmers Report</h5>
            <div>
                <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel()">
                    <i class="fas fa-file-excel me-1"></i> Export Excel
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="farmersTable" class="table table-striped table-bordered text-nowrap w-100">
                    <thead>
                        <tr>
                            <th>Farmer Code</th>
                            <th>Name</th>
                            <th>LLG</th>
                            <th>Ward</th>
                            <th>Contacts</th>
                            <th>Email</th>
                            <th>Crop Blocks</th>
                            <th>Livestock Blocks</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($farmers as $farmer): ?>
                            <tr>
                                <td><?= esc($farmer['farmer_code']) ?></td>
                                <td><?= esc($farmer['given_name']) . ' ' . esc($farmer['surname']) ?></td>
                                <td><?= esc($farmer['llg_name'] ?? 'N/A') ?></td>
                                <td><?= esc($farmer['ward_name'] ?? 'N/A') ?></td>
                                <td><?= esc($farmer['phone'] ?? 'N/A') ?></td>
                                <td><?= esc($farmer['email'] ?? 'N/A') ?></td>
                                <td class="text-center">
                                    <span class="badge bg-success"><?= $farmer['crop_blocks_count'] ?></span>
                                </td>
                                <td class="text-center">
                                    <span class="badge bg-warning"><?= $farmer['livestock_blocks_count'] ?></span>
                                </td>
                                <td class="text-center">
                                    <a href="<?= base_url('staff/reports/farmer_profile/' . $farmer['id']) ?>"
                                       class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye me-1"></i> View Profile
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

</div>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // Initialize DataTable
        $('#farmersTable').DataTable({
            dom: '<"row"<"col-sm-12 col-md-6"B><"col-sm-12 col-md-6"f>>rtip',
            buttons: [
                {
                    extend: 'excel',
                    className: 'btn btn-success btn-sm',
                    text: '<i class="fas fa-file-excel me-1"></i> Excel'
                },
                {
                    extend: 'pdf',
                    className: 'btn btn-danger btn-sm',
                    text: '<i class="fas fa-file-pdf me-1"></i> PDF'
                },
                {
                    extend: 'print',
                    className: 'btn btn-info btn-sm',
                    text: '<i class="fas fa-print me-1"></i> Print'
                }
            ],
            pageLength: 25,
            order: [[0, 'asc']], // Sort by farmer code
            scrollX: true,
            responsive: true,
            columnDefs: [
                {
                    className: 'text-center',
                    targets: [6, 7, 8] // Crop blocks, livestock blocks, action columns
                }
            ]
        });
    });

    function exportToExcel() {
        $('.buttons-excel').click();
    }
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
