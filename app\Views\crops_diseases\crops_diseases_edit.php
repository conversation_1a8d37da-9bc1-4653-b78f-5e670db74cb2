<?= $this->extend('templates/staff_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-0">Edit Disease Data</h4>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="<?= base_url('staff') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/crops/diseases') ?>">Disease Data</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('staff/crops/diseases/view/' . $block['id']) ?>">Block <?= esc($block['block_code']) ?></a></li>
                            <li class="breadcrumb-item active" aria-current="page">Edit Disease Data</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('staff/crops/diseases/view/' . $block['id']) ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Block
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Disease Data for Block <?= esc($block['block_code']) ?>
                    </h5>
                    <p class="card-text mb-0 text-muted">Update disease information for this farm block</p>
                </div>
                <div class="card-body">
                    <!-- Success/Error Messages -->
                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" action="<?= base_url('staff/crops/diseases/update/' . $disease_data['id']) ?>">
                        <?= csrf_field() ?>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="disease_type_id" class="form-label">Disease Type <span class="text-danger">*</span></label>
                                <select name="disease_type_id" id="disease_type_id" class="form-select" required>
                                    <option value="">Select Disease Type</option>
                                    <?php foreach ($infections as $infection): ?>
                                        <option value="<?= esc($infection['id']) ?>" 
                                                <?= (old('disease_type_id') ?: $disease_data['disease_type_id']) == $infection['id'] ? 'selected' : '' ?>>
                                            <?= esc($infection['name']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="disease_name" class="form-label">Disease Name <span class="text-danger">*</span></label>
                                <input type="text" name="disease_name" id="disease_name" class="form-control" 
                                       value="<?= old('disease_name') ?: esc($disease_data['disease_name']) ?>" required>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea name="description" id="description" class="form-control" rows="3"><?= old('description') ?: esc($disease_data['description']) ?></textarea>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="number_of_plants" class="form-label">Number of Plants Affected <span class="text-danger">*</span></label>
                                <input type="number" name="number_of_plants" id="number_of_plants" class="form-control" 
                                       value="<?= old('number_of_plants') ?: esc($disease_data['number_of_plants']) ?>" min="0" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="breed" class="form-label">Breed</label>
                                <input type="text" name="breed" id="breed" class="form-control" 
                                       value="<?= old('breed') ?: esc($disease_data['breed']) ?>">
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="action_date" class="form-label">Action Date <span class="text-danger">*</span></label>
                                <input type="date" name="action_date" id="action_date" class="form-control" 
                                       value="<?= old('action_date') ?: date('Y-m-d', strtotime($disease_data['action_date'])) ?>" required>
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="hectares" class="form-label">Hectares <span class="text-danger">*</span></label>
                                <input type="number" name="hectares" id="hectares" class="form-control" 
                                       value="<?= old('hectares') ?: esc($disease_data['hectares']) ?>" step="0.01" min="0" required>
                            </div>
                            
                            <div class="col-12 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea name="remarks" id="remarks" class="form-control" rows="3"><?= old('remarks') ?: esc($disease_data['remarks']) ?></textarea>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?= base_url('staff/crops/diseases/view/' . $block['id']) ?>" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Disease Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
